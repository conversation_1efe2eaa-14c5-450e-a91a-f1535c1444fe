/// <reference types="node" />
import { BinaryToTextEncoding } from 'crypto';
export declare function formateDate(timestamp: number): string;
export declare function second(): number;
export declare function stringify(v: any): string;
export declare function sha256hash(string: string, encoding?: BinaryToTextEncoding): string;
export declare function sha256hmac(string: string, secret?: string | Buffer, encoding?: BinaryToTextEncoding): string | Buffer;
export declare function isNodeEnv(): boolean;
