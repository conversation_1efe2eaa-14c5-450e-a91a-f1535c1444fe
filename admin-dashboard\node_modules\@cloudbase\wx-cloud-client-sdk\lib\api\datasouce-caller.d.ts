import { MethodResponse, CallDataSourceParams, RunMysqlCommandParams } from '../types';
export declare const enum EQUERY_PARAM_TYPE {
    ARRAY = "ARRAY",
    BOOLEAN = "BOOLEAN",
    NUMBER = "NUMBER",
    OBJECT = "OBJECT",
    STRING = "STRING"
}
export declare const callDataSource: ({ dataSourceName, methodName, params, realMethodName, callFunction, envType, mode, }: CallDataSourceParams) => Promise<MethodResponse<any>>;
export declare const runMysqlCommand: ({ sql, params, config, callFunction, unsafe }: RunMysqlCommandParams) => Promise<MethodResponse<any>>;
