import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import '../../controllers/smart_composer_controller.dart';
import '../../models/smart_composer_models.dart';
import '../../models/novel.dart';
import '../../widgets/themed_dropdown.dart';
import 'smart_composer_settings_screen.dart';

/// Smart Composer 聊天界面
class SmartComposerChatScreen extends StatefulWidget {
  const SmartComposerChatScreen({super.key});

  @override
  State<SmartComposerChatScreen> createState() => _SmartComposerChatScreenState();
}

class _SmartComposerChatScreenState extends State<SmartComposerChatScreen> {
  final SmartComposerController _controller = Get.find<SmartComposerController>();
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  
  ChatSession? _session;
  Novel? _novel;
  Chapter? _currentChapter;
  int _currentChapterIndex = 0;
  bool _isEditingMode = false;

  @override
  void initState() {
    super.initState();
    _initializeFromArguments();
  }

  void _initializeFromArguments() {
    final args = Get.arguments as Map<String, dynamic>?;
    if (args != null) {
      _session = args['session'] as ChatSession?;
      _novel = args['novel'] as Novel?;
      _currentChapter = args['chapter'] as Chapter?;
      
      if (_session != null) {
        _controller.switchToSession(_session!);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Obx(() {
          final session = _controller.currentSession.value;
          return Text(session?.title ?? 'AI 写作助手');
        }),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _showSettingsDialog,
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'new_chat':
                  _startNewChat();
                  break;
                case 'clear_history':
                  _clearChatHistory();
                  break;
                case 'export_chat':
                  _exportChat();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'new_chat',
                child: Row(
                  children: [
                    Icon(Icons.add_comment),
                    SizedBox(width: 8),
                    Text('新对话'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'clear_history',
                child: Row(
                  children: [
                    Icon(Icons.clear_all),
                    SizedBox(width: 8),
                    Text('清空历史'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'export_chat',
                child: Row(
                  children: [
                    Icon(Icons.download),
                    SizedBox(width: 8),
                    Text('导出对话'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // 上下文信息栏
          if (_novel != null) _buildContextBar(),

          // 聊天消息列表
          Expanded(
            child: Obx(() => _buildMessageList()),
          ),

          // 输入框
          _buildInputArea(),
        ],
      ),
    );
  }

  /// 构建上下文信息栏
  Widget _buildContextBar() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 0.5,
          ),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                Icons.book,
                size: 16,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  '正在为《${_novel!.title}》提供写作建议',
                  style: TextStyle(
                    fontSize: 12,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ),
              // 编辑模式切换按钮
              IconButton(
                icon: Icon(
                  _isEditingMode ? Icons.visibility : Icons.edit,
                  size: 16,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                onPressed: () {
                  setState(() {
                    _isEditingMode = !_isEditingMode;
                  });
                },
                tooltip: _isEditingMode ? '查看模式' : '编辑模式',
                padding: const EdgeInsets.all(4),
                constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
              ),
            ],
          ),

          // 章节选择器和快捷操作（仅在有章节时显示）
          if (_novel!.chapters.isNotEmpty) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: DialogSelector<int>(
                    value: _currentChapterIndex,
                    title: '选择章节',
                    hint: '选择要编辑的章节',
                    items: _novel!.chapters.asMap().entries.map((entry) {
                      final index = entry.key;
                      final chapter = entry.value;
                      return DialogSelectItem<int>(
                        value: index,
                        text: '第${chapter.number}章：${chapter.title}',
                        leading: Icon(
                          Icons.article,
                          color: Theme.of(context).colorScheme.primary,
                          size: 20,
                        ),
                        trailing: Text(
                          '${chapter.content.length}字',
                          style: TextStyle(
                            fontSize: 12,
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                        ),
                      );
                    }).toList(),
                    onChanged: (int? newIndex) {
                      if (newIndex != null) {
                        setState(() {
                          _currentChapterIndex = newIndex;
                          _currentChapter = _novel!.chapters[newIndex];
                        });
                      }
                    },
                    decoration: InputDecoration(
                      isDense: true,
                      contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(4),
                        borderSide: BorderSide(
                          color: Theme.of(context).colorScheme.onSurfaceVariant.withOpacity(0.3),
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                // 快捷操作按钮
                _buildQuickActionButtons(),
              ],
            ),
          ],
        ],
      ),
    );
  }

  /// 构建快捷操作按钮
  Widget _buildQuickActionButtons() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildQuickActionButton(
          icon: Icons.add,
          label: '续写',
          onPressed: () => _quickAction('续写'),
        ),
        const SizedBox(width: 4),
        _buildQuickActionButton(
          icon: Icons.edit,
          label: '润色',
          onPressed: () => _quickAction('润色'),
        ),
        const SizedBox(width: 4),
        _buildQuickActionButton(
          icon: Icons.refresh,
          label: '改写',
          onPressed: () => _quickAction('改写'),
        ),
      ],
    );
  }

  /// 构建单个快捷操作按钮
  Widget _buildQuickActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
  }) {
    return Tooltip(
      message: label,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(4),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 12,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 2),
              Text(
                label,
                style: TextStyle(
                  fontSize: 10,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 快捷操作处理
  void _quickAction(String action) {
    if (_novel == null || _currentChapter == null) {
      Get.snackbar('提示', '请先选择要编辑的章节');
      return;
    }

    String prompt;
    switch (action) {
      case '续写':
        prompt = '请为当前章节续写一段内容，保持故事的连贯性和风格一致性。';
        break;
      case '润色':
        prompt = '请润色当前章节的文字，使其更加生动优美，但保持原意不变。';
        break;
      case '改写':
        prompt = '请改写当前章节，使其更加精彩，可以调整表达方式和细节描述。';
        break;
      default:
        prompt = '请帮我优化当前章节的内容。';
    }

    _messageController.text = prompt;
    _sendMessage();
  }



  /// 构建消息列表
  Widget _buildMessageList() {
    final session = _controller.currentSession.value;
    if (session == null || session.messages.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.smart_toy,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              '开始与 AI 写作助手对话',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '我可以帮助您：\n• 完善小说情节\n• 优化文字表达\n• 提供创作建议\n• 解答写作问题',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      itemCount: session.messages.length,
      itemBuilder: (context, index) {
        final message = session.messages[index];
        return _buildMessageBubble(message);
      },
    );
  }

  /// 构建消息气泡
  Widget _buildMessageBubble(ChatMessage message) {
    final isUser = message.role == 'user';
    
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        mainAxisAlignment: isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isUser) ...[
            CircleAvatar(
              radius: 16,
              backgroundColor: Theme.of(context).colorScheme.primary,
              child: const Icon(
                Icons.smart_toy,
                size: 16,
                color: Colors.white,
              ),
            ),
            const SizedBox(width: 8),
          ],
          Flexible(
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: isUser
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.surfaceVariant,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildMessageContent(message, isUser),
                  const SizedBox(height: 4),
                  Text(
                    _formatTime(message.timestamp),
                    style: TextStyle(
                      fontSize: 10,
                      color: isUser
                          ? Theme.of(context).colorScheme.onPrimary.withOpacity(0.7)
                          : Theme.of(context).colorScheme.onSurfaceVariant.withOpacity(0.7),
                    ),
                  ),
                ],
              ),
            ),
          ),
          if (isUser) ...[
            const SizedBox(width: 8),
            CircleAvatar(
              radius: 16,
              backgroundColor: Theme.of(context).colorScheme.secondary,
              child: const Icon(
                Icons.person,
                size: 16,
                color: Colors.white,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建消息内容（支持markdown）
  Widget _buildMessageContent(ChatMessage message, bool isUser) {
    final textColor = isUser
        ? Theme.of(context).colorScheme.onPrimary
        : Theme.of(context).colorScheme.onSurfaceVariant;

    // 检查是否包含markdown语法
    if (!isUser && _containsMarkdown(message.content)) {
      return MarkdownBody(
        data: message.content,
        selectable: true,
        styleSheet: MarkdownStyleSheet(
          p: TextStyle(
            fontSize: 14,
            color: textColor,
            height: 1.4,
          ),
          h1: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: textColor,
          ),
          h2: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: textColor,
          ),
          h3: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: textColor,
          ),
          strong: TextStyle(
            fontWeight: FontWeight.bold,
            color: textColor,
          ),
          em: TextStyle(
            fontStyle: FontStyle.italic,
            color: textColor,
          ),
          code: TextStyle(
            fontSize: 13,
            fontFamily: 'monospace',
            backgroundColor: Theme.of(context).brightness == Brightness.dark
                ? Colors.grey[800]
                : Colors.grey[200],
            color: textColor,
          ),
          codeblockDecoration: BoxDecoration(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.grey[850]
                : Colors.grey[100],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.grey[700]!
                  : Colors.grey[300]!,
            ),
          ),
          blockquote: TextStyle(
            fontSize: 14,
            fontStyle: FontStyle.italic,
            color: textColor.withOpacity(0.8),
          ),
          blockquoteDecoration: BoxDecoration(
            border: Border(
              left: BorderSide(
                color: Theme.of(context).primaryColor,
                width: 4,
              ),
            ),
          ),
          listBullet: TextStyle(
            color: textColor,
          ),
        ),
      );
    } else {
      return SelectableText(
        message.content,
        style: TextStyle(
          color: textColor,
        ),
      );
    }
  }

  /// 检查文本是否包含markdown语法
  bool _containsMarkdown(String text) {
    final markdownPatterns = [
      RegExp(r'\*\*.*?\*\*'), // 粗体
      RegExp(r'\*.*?\*'), // 斜体
      RegExp(r'`.*?`'), // 行内代码
      RegExp(r'```[\s\S]*?```'), // 代码块
      RegExp(r'^#{1,6}\s', multiLine: true), // 标题
      RegExp(r'^\s*[-*+]\s', multiLine: true), // 列表
      RegExp(r'^\s*\d+\.\s', multiLine: true), // 有序列表
      RegExp(r'\[.*?\]\(.*?\)'), // 链接
      RegExp(r'^>\s', multiLine: true), // 引用
    ];

    return markdownPatterns.any((pattern) => pattern.hasMatch(text));
  }

  /// 构建输入区域
  Widget _buildInputArea() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 0.5,
          ),
        ),
      ),
      child: Column(
        children: [


          // 编辑模式提示
          if (_isEditingMode && _novel != null && _currentChapter != null)
            Container(
              padding: const EdgeInsets.all(8),
              margin: const EdgeInsets.only(bottom: 8),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.edit,
                    size: 16,
                    color: Theme.of(context).colorScheme.onPrimaryContainer,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '编辑模式：正在编辑第${_currentChapter!.number}章',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onPrimaryContainer,
                      ),
                    ),
                  ),
                  Text(
                    '${_currentChapter!.content.length}字',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onPrimaryContainer.withOpacity(0.7),
                    ),
                  ),
                ],
              ),
            ),

          // 输入行
          Row(
            children: [
              // 输入框
              Expanded(
                child: TextField(
                  controller: _messageController,
                  maxLines: null,
                  decoration: InputDecoration(
                    hintText: _isEditingMode
                        ? '输入编辑指令（如：续写、润色、改写）...'
                        : '输入您的问题或需求...',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(24),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                    prefixIcon: _isEditingMode
                        ? Icon(
                            Icons.edit,
                            color: Theme.of(context).colorScheme.primary,
                          )
                        : null,
                  ),
                  onSubmitted: (_) => _sendMessage(),
                ),
              ),
              const SizedBox(width: 8),
              Obx(() => FloatingActionButton(
                    mini: true,
                    onPressed: _controller.isLoading.value ? null : _sendMessage,
                    child: _controller.isLoading.value
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.send),
                  )),
            ],
          ),
        ],
      ),
    );
  }

  /// 发送消息
  void _sendMessage() {
    final text = _messageController.text.trim();
    if (text.isEmpty) return;

    _messageController.clear();

    // 如果在编辑模式且有当前章节，发送小说编辑请求
    if (_isEditingMode && _novel != null && _currentChapter != null) {
      _sendNovelEditMessage(text);
    } else {
      // 发送普通聊天消息
      _controller.sendMessage(
        content: text,
        novel: _novel,
        chapter: _currentChapter,
      );
    }

    // 滚动到底部
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  /// 发送小说编辑消息
  void _sendNovelEditMessage(String instruction) async {
    if (_novel == null || _currentChapter == null) {
      Get.snackbar('错误', '请先选择要编辑的章节');
      return;
    }

    try {
      // 构建编辑上下文
      final context = _buildEditingContext(instruction);

      // 发送带有小说编辑上下文的消息
      await _controller.sendMessage(
        content: context,
        novel: _novel,
        chapter: _currentChapter,
      );

      // 显示编辑提示
      Get.snackbar(
        '编辑请求已发送',
        '正在为《${_novel!.title}》第${_currentChapter!.number}章生成编辑建议...',
        duration: const Duration(seconds: 2),
      );
    } catch (e) {
      Get.snackbar('错误', '发送编辑请求失败: $e');
    }
  }

  /// 构建编辑上下文
  String _buildEditingContext(String instruction) {
    final buffer = StringBuffer();

    buffer.writeln('【小说编辑请求】');
    buffer.writeln('小说：《${_novel!.title}》');
    buffer.writeln('章节：第${_currentChapter!.number}章 - ${_currentChapter!.title}');
    buffer.writeln('');
    buffer.writeln('当前章节内容：');
    buffer.writeln('```');
    buffer.writeln(_currentChapter!.content);
    buffer.writeln('```');
    buffer.writeln('');
    buffer.writeln('编辑指令：$instruction');
    buffer.writeln('');
    buffer.writeln('请根据上述指令对章节内容进行编辑，并提供修改建议。如果是续写请求，请在现有内容基础上继续创作。');

    return buffer.toString();
  }



  /// 格式化时间
  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  /// 显示设置对话框
  void _showSettingsDialog() {
    Get.to(() => const SmartComposerSettingsScreen());
  }

  /// 开始新对话
  void _startNewChat() {
    final session = _controller.createNewSession(
      title: _novel != null ? '《${_novel!.title}》写作助手' : '新对话',
      novelId: _novel?.id,
      context: _novel != null ? {
        'novel_title': _novel!.title,
        'novel_genre': _novel!.genre,
        'novel_outline': _novel!.outline,
        'chapter_count': _novel!.chapters.length,
      } : null,
    );
    
    setState(() {
      _session = session;
    });
  }

  /// 清空聊天历史
  void _clearChatHistory() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('清空历史'),
        content: const Text('确定要清空当前对话的所有消息吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _startNewChat();
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  /// 导出对话
  void _exportChat() {
    // TODO: 实现导出对话功能
    Get.snackbar('提示', '导出功能正在开发中');
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }
}
