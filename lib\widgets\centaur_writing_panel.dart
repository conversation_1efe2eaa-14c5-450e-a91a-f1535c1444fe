import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:novel_app/controllers/api_config_controller.dart';
import 'package:novel_app/controllers/knowledge_base_controller.dart';
import 'package:novel_app/controllers/writing_style_package_controller.dart';
import 'package:novel_app/services/character_card_service.dart';
import 'package:novel_app/models/novel.dart';
import 'package:novel_app/models/character_card.dart';
import 'package:novel_app/models/writing_style_package.dart';
import 'package:novel_app/models/knowledge_base.dart';

/// 半人马写作面板组件
/// 提供AI辅助写作的各种配置选项和生成功能
class CentaurWritingPanel extends StatefulWidget {
  final Novel novel;
  final int currentChapterIndex;
  final TextEditingController promptController;
  final bool isGenerating;
  final VoidCallback onGenerate;
  final Function(Set<String>) onKnowledgeSelectionChanged;
  final Function(Set<String>) onCharacterSelectionChanged;
  final Function(Set<int>) onContextSelectionChanged;
  final Function(String?) onWritingStyleChanged;
  final Function(bool) onAllowCreateCharactersChanged;

  const CentaurWritingPanel({
    super.key,
    required this.novel,
    required this.currentChapterIndex,
    required this.promptController,
    required this.isGenerating,
    required this.onGenerate,
    required this.onKnowledgeSelectionChanged,
    required this.onCharacterSelectionChanged,
    required this.onContextSelectionChanged,
    required this.onWritingStyleChanged,
    required this.onAllowCreateCharactersChanged,
  });

  @override
  State<CentaurWritingPanel> createState() => _CentaurWritingPanelState();
}

class _CentaurWritingPanelState extends State<CentaurWritingPanel> {
  final _selectedKnowledgeIds = <String>{};
  final _selectedCharacterIds = <String>{};
  final _selectedContextChapters = <int>{};
  String? _selectedWritingStyleId;
  bool _allowCreateNewCharacters = true;

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Theme.of(context).scaffoldBackgroundColor,
      child: Column(
        children: [
          // 标题栏
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              border: Border(
                bottom: BorderSide(
                  color: Theme.of(context).dividerColor,
                ),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.auto_awesome,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  '半人马写作',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ],
            ),
          ),
          // 内容区域
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildModelSelector(),
                  const SizedBox(height: 16),
                  _buildKnowledgeBaseSelector(),
                  const SizedBox(height: 16),
                  _buildWritingStyleSelector(),
                  const SizedBox(height: 16),
                  _buildCharacterSelector(),
                  const SizedBox(height: 16),
                  _buildContextSelector(),
                  const SizedBox(height: 16),
                  _buildCharacterCreationToggle(),
                  const SizedBox(height: 16),
                  _buildPromptInput(),
                  const SizedBox(height: 16),
                  _buildGenerateButton(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建模型选择器
  Widget _buildModelSelector() {
    final apiController = Get.find<ApiConfigController>();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '模型选择',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Obx(() => Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            border: Border.all(color: Theme.of(context).dividerColor),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(
                Icons.smart_toy,
                size: 16,
                color: Theme.of(context).primaryColor,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  apiController.selectedModelId.value,
                  style: const TextStyle(fontSize: 14),
                ),
              ),
              IconButton(
                icon: const Icon(Icons.settings, size: 16),
                onPressed: () => Get.toNamed('/settings'),
                tooltip: '模型设置',
              ),
            ],
          ),
        )),
      ],
    );
  }

  /// 构建知识库选择器（弹窗式）
  Widget _buildKnowledgeBaseSelector() {
    final knowledgeController = Get.find<KnowledgeBaseController>();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Text(
              '知识库',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const Spacer(),
            Obx(() => Switch(
              value: knowledgeController.useKnowledgeBase.value,
              onChanged: (value) {
                knowledgeController.useKnowledgeBase.value = value;
                knowledgeController.saveSettings();
              },
            )),
          ],
        ),
        const SizedBox(height: 8),
        Obx(() => knowledgeController.useKnowledgeBase.value
            ? _buildKnowledgeSelectionButton()
            : Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Theme.of(context).disabledColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Text(
                  '知识库已禁用',
                  style: TextStyle(fontSize: 14),
                ),
              )),
      ],
    );
  }

  /// 构建知识库选择按钮
  Widget _buildKnowledgeSelectionButton() {
    final knowledgeController = Get.find<KnowledgeBaseController>();
    
    return InkWell(
      onTap: () => _showKnowledgeSelectionDialog(),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
        decoration: BoxDecoration(
          border: Border.all(color: Theme.of(context).dividerColor),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(
              Icons.library_books,
              size: 16,
              color: Theme.of(context).primaryColor,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                _selectedKnowledgeIds.isEmpty
                    ? '选择知识库文档'
                    : '已选择 ${_selectedKnowledgeIds.length} 个文档',
                style: TextStyle(
                  fontSize: 14,
                  color: _selectedKnowledgeIds.isEmpty
                      ? Colors.grey.shade600
                      : Theme.of(context).colorScheme.onSurface,
                ),
              ),
            ),
            Icon(
              Icons.arrow_drop_down,
              color: Theme.of(context).primaryColor,
            ),
          ],
        ),
      ),
    );
  }

  /// 显示知识库选择弹窗
  void _showKnowledgeSelectionDialog() {
    final knowledgeController = Get.find<KnowledgeBaseController>();
    final tempSelected = Set<String>.from(_selectedKnowledgeIds);

    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.8,
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.7,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 标题栏
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primaryContainer,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        '选择知识库文档',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onPrimaryContainer,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => Get.back(),
                      icon: Icon(
                        Icons.close,
                        color: Theme.of(context).colorScheme.onPrimaryContainer,
                      ),
                    ),
                  ],
                ),
              ),

              // 文档列表
              Flexible(
                child: Obx(() => ListView.builder(
                  shrinkWrap: true,
                  itemCount: knowledgeController.documents.length,
                  itemBuilder: (context, index) {
                    final doc = knowledgeController.documents[index];
                    final isSelected = tempSelected.contains(doc.id);

                    return CheckboxListTile(
                      title: Text(doc.title),
                      subtitle: Text(doc.category),
                      value: isSelected,
                      onChanged: (value) {
                        if (value == true) {
                          tempSelected.add(doc.id);
                        } else {
                          tempSelected.remove(doc.id);
                        }
                      },
                    );
                  },
                )),
              ),

              // 按钮栏
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: () => Get.back(),
                      child: const Text('取消'),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton(
                      onPressed: () {
                        setState(() {
                          _selectedKnowledgeIds.clear();
                          _selectedKnowledgeIds.addAll(tempSelected);
                        });
                        widget.onKnowledgeSelectionChanged(_selectedKnowledgeIds);
                        Get.back();
                      },
                      child: const Text('确定'),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建文风包选择器（弹窗式）
  Widget _buildWritingStyleSelector() {
    try {
      final styleController = Get.find<WritingStylePackageController>();

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '文风包',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Obx(() => InkWell(
            onTap: () => _showWritingStyleSelectionDialog(),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
              decoration: BoxDecoration(
                border: Border.all(color: Theme.of(context).dividerColor),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.style,
                    size: 16,
                    color: Theme.of(context).primaryColor,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _getSelectedWritingStyleName(styleController),
                      style: TextStyle(
                        fontSize: 14,
                        color: _selectedWritingStyleId == null
                            ? Colors.grey.shade600
                            : Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                  ),
                  Icon(
                    Icons.arrow_drop_down,
                    color: Theme.of(context).primaryColor,
                  ),
                ],
              ),
            ),
          )),
        ],
      );
    } catch (e) {
      return const SizedBox.shrink();
    }
  }

  /// 获取选中的文风包名称
  String _getSelectedWritingStyleName(WritingStylePackageController controller) {
    if (_selectedWritingStyleId == null) {
      return '选择文风包';
    }
    final package = controller.packages.firstWhereOrNull(
      (p) => p.id == _selectedWritingStyleId,
    );
    return package?.name ?? '未知文风包';
  }

  /// 显示文风包选择弹窗
  void _showWritingStyleSelectionDialog() {
    final styleController = Get.find<WritingStylePackageController>();

    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.8,
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.6,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 标题栏
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primaryContainer,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        '选择文风包',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onPrimaryContainer,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => Get.back(),
                      icon: Icon(
                        Icons.close,
                        color: Theme.of(context).colorScheme.onPrimaryContainer,
                      ),
                    ),
                  ],
                ),
              ),

              // 文风包列表
              Flexible(
                child: Obx(() => ListView(
                  shrinkWrap: true,
                  children: [
                    // 无选择选项
                    ListTile(
                      leading: Icon(
                        Icons.clear,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                      title: const Text('无'),
                      trailing: _selectedWritingStyleId == null
                          ? Icon(Icons.check, color: Theme.of(context).primaryColor)
                          : null,
                      selected: _selectedWritingStyleId == null,
                      onTap: () {
                        setState(() {
                          _selectedWritingStyleId = null;
                        });
                        widget.onWritingStyleChanged(null);
                        Get.back();
                      },
                    ),
                    const Divider(),
                    // 文风包选项
                    ...styleController.packages.map((package) {
                      final isSelected = _selectedWritingStyleId == package.id;
                      return ListTile(
                        leading: Icon(
                          Icons.style,
                          color: Theme.of(context).primaryColor,
                        ),
                        title: Text(package.name),
                        subtitle: package.description.isNotEmpty
                            ? Text(package.description)
                            : null,
                        trailing: isSelected
                            ? Icon(Icons.check, color: Theme.of(context).primaryColor)
                            : null,
                        selected: isSelected,
                        onTap: () {
                          setState(() {
                            _selectedWritingStyleId = package.id;
                          });
                          widget.onWritingStyleChanged(package.id);
                          Get.back();
                        },
                      );
                    }),
                  ],
                )),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建角色选择器（弹窗式）
  Widget _buildCharacterSelector() {
    try {
      final characterService = Get.find<CharacterCardService>();

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '角色卡片',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Obx(() => InkWell(
            onTap: () => _showCharacterSelectionDialog(),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
              decoration: BoxDecoration(
                border: Border.all(color: Theme.of(context).dividerColor),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.person,
                    size: 16,
                    color: Theme.of(context).primaryColor,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      characterService.cards.isEmpty
                          ? '暂无角色卡片'
                          : _selectedCharacterIds.isEmpty
                              ? '选择角色卡片'
                              : '已选择 ${_selectedCharacterIds.length} 个角色',
                      style: TextStyle(
                        fontSize: 14,
                        color: characterService.cards.isEmpty || _selectedCharacterIds.isEmpty
                            ? Colors.grey.shade600
                            : Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                  ),
                  if (characterService.cards.isNotEmpty)
                    Icon(
                      Icons.arrow_drop_down,
                      color: Theme.of(context).primaryColor,
                    ),
                ],
              ),
            ),
          )),
        ],
      );
    } catch (e) {
      return const SizedBox.shrink();
    }
  }

  /// 显示角色选择弹窗
  void _showCharacterSelectionDialog() {
    final characterService = Get.find<CharacterCardService>();
    final tempSelected = Set<String>.from(_selectedCharacterIds);

    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.8,
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.7,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 标题栏
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primaryContainer,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        '选择角色卡片',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onPrimaryContainer,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => Get.back(),
                      icon: Icon(
                        Icons.close,
                        color: Theme.of(context).colorScheme.onPrimaryContainer,
                      ),
                    ),
                  ],
                ),
              ),

              // 角色列表
              Flexible(
                child: Obx(() => characterService.cards.isEmpty
                    ? const Padding(
                        padding: EdgeInsets.all(32),
                        child: Text(
                          '暂无角色卡片',
                          style: TextStyle(fontSize: 16),
                          textAlign: TextAlign.center,
                        ),
                      )
                    : ListView.builder(
                        shrinkWrap: true,
                        itemCount: characterService.cards.length,
                        itemBuilder: (context, index) {
                          final character = characterService.cards[index];
                          final isSelected = tempSelected.contains(character.id);

                          return CheckboxListTile(
                            title: Text(character.name),
                            subtitle: Text(
                              character.characterTypeId.isNotEmpty
                                  ? character.characterTypeId
                                  : '未分类',
                            ),
                            value: isSelected,
                            onChanged: (value) {
                              if (value == true) {
                                tempSelected.add(character.id);
                              } else {
                                tempSelected.remove(character.id);
                              }
                            },
                          );
                        },
                      )),
              ),

              // 按钮栏
              if (characterService.cards.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TextButton(
                        onPressed: () => Get.back(),
                        child: const Text('取消'),
                      ),
                      const SizedBox(width: 8),
                      ElevatedButton(
                        onPressed: () {
                          setState(() {
                            _selectedCharacterIds.clear();
                            _selectedCharacterIds.addAll(tempSelected);
                          });
                          widget.onCharacterSelectionChanged(_selectedCharacterIds);
                          Get.back();
                        },
                        child: const Text('确定'),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
