import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 弹窗选择项数据模型
class DialogSelectItem<T> {
  final T value;
  final String text;
  final Widget? leading;
  final Widget? trailing;
  final bool enabled;

  const DialogSelectItem({
    required this.value,
    required this.text,
    this.leading,
    this.trailing,
    this.enabled = true,
  });
}

/// 弹窗式选择器组件
/// 替代传统的下拉选择，提供更好的用户体验
class DialogSelector<T> extends StatelessWidget {
  final T? value;
  final String? hint;
  final List<DialogSelectItem<T>> items;
  final ValueChanged<T?>? onChanged;
  final String title;
  final Widget? icon;
  final bool isExpanded;
  final InputDecoration? decoration;
  final FormFieldValidator<T>? validator;
  final bool enabled;

  const DialogSelector({
    super.key,
    this.value,
    this.hint,
    required this.items,
    this.onChanged,
    this.title = '请选择',
    this.icon,
    this.isExpanded = true,
    this.decoration,
    this.validator,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    final selectedItem = items.where((item) => item.value == value).firstOrNull;
    final displayText = selectedItem?.text ?? hint ?? '请选择';

    return InkWell(
      onTap: enabled ? () => _showSelectionDialog(context) : null,
      child: decoration != null
          ? InputDecorator(
              decoration: decoration!,
              child: _buildContent(context, displayText),
            )
          : Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                border: Border.all(color: Theme.of(context).dividerColor),
                borderRadius: BorderRadius.circular(4),
              ),
              child: _buildContent(context, displayText),
            ),
    );
  }

  Widget _buildContent(BuildContext context, String displayText) {
    return Row(
      children: [
        if (isExpanded)
          Expanded(
            child: Text(
              displayText,
              style: TextStyle(
                color: enabled
                    ? Theme.of(context).colorScheme.onSurface
                    : Theme.of(context).disabledColor,
              ),
            ),
          )
        else
          Text(
            displayText,
            style: TextStyle(
              color: enabled
                  ? Theme.of(context).colorScheme.onSurface
                  : Theme.of(context).disabledColor,
            ),
          ),
        const SizedBox(width: 8),
        icon ?? Icon(
          Icons.arrow_drop_down,
          color: enabled
              ? Theme.of(context).colorScheme.onSurface
              : Theme.of(context).disabledColor,
        ),
      ],
    );
  }

  void _showSelectionDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          contentPadding: const EdgeInsets.symmetric(vertical: 16),
          content: SizedBox(
            width: double.maxFinite,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: items.length,
              itemBuilder: (context, index) {
                final item = items[index];
                final isSelected = item.value == value;

                return ListTile(
                  enabled: item.enabled,
                  leading: item.leading,
                  title: Text(
                    item.text,
                    style: TextStyle(
                      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                      color: isSelected
                          ? Theme.of(context).colorScheme.primary
                          : null,
                    ),
                  ),
                  trailing: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (item.trailing != null) item.trailing!,
                      if (isSelected)
                        Icon(
                          Icons.check,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                    ],
                  ),
                  onTap: item.enabled ? () {
                    Navigator.of(context).pop();
                    onChanged?.call(item.value);
                  } : null,
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
          ],
        );
      },
    );
  }
}

/// 弹窗式表单字段选择器
/// 提供表单验证功能的弹窗选择器
class DialogSelectorFormField<T> extends FormField<T> {
  final List<DialogSelectItem<T>> items;
  final String title;
  final String? hint;
  final Widget? icon;
  final bool isExpanded;
  final bool enabled;
  final ValueChanged<T?>? onChanged;

  DialogSelectorFormField({
    super.key,
    T? initialValue,
    required this.items,
    this.title = '请选择',
    this.hint,
    this.icon,
    this.isExpanded = true,
    this.enabled = true,
    this.onChanged,
    super.onSaved,
    super.validator,
    super.autovalidateMode,
    InputDecoration? decoration,
  }) : super(
          initialValue: initialValue,
          builder: (FormFieldState<T> state) {
            final selectedItem = items.where((item) => item.value == state.value).firstOrNull;
            final displayText = selectedItem?.text ?? hint ?? '请选择';

            return InkWell(
              onTap: enabled ? () => _showSelectionDialog(state.context, state, items, title, onChanged) : null,
              child: InputDecorator(
                decoration: (decoration ?? const InputDecoration()).copyWith(
                  errorText: state.errorText,
                ),
                child: Row(
                  children: [
                    if (isExpanded)
                      Expanded(
                        child: Text(
                          displayText,
                          style: TextStyle(
                            color: enabled
                                ? Theme.of(state.context).colorScheme.onSurface
                                : Theme.of(state.context).disabledColor,
                          ),
                        ),
                      )
                    else
                      Text(
                        displayText,
                        style: TextStyle(
                          color: enabled
                              ? Theme.of(state.context).colorScheme.onSurface
                              : Theme.of(state.context).disabledColor,
                        ),
                      ),
                    const SizedBox(width: 8),
                    icon ?? Icon(
                      Icons.arrow_drop_down,
                      color: enabled
                          ? Theme.of(state.context).colorScheme.onSurface
                          : Theme.of(state.context).disabledColor,
                    ),
                  ],
                ),
              ),
            );
          },
        );

  static void _showSelectionDialog<T>(
    BuildContext context,
    FormFieldState<T> state,
    List<DialogSelectItem<T>> items,
    String title,
    ValueChanged<T?>? onChanged,
  ) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          contentPadding: const EdgeInsets.symmetric(vertical: 16),
          content: SizedBox(
            width: double.maxFinite,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: items.length,
              itemBuilder: (context, index) {
                final item = items[index];
                final isSelected = item.value == state.value;

                return ListTile(
                  enabled: item.enabled,
                  leading: item.leading,
                  title: Text(
                    item.text,
                    style: TextStyle(
                      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                      color: isSelected
                          ? Theme.of(context).colorScheme.primary
                          : null,
                    ),
                  ),
                  trailing: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (item.trailing != null) item.trailing!,
                      if (isSelected)
                        Icon(
                          Icons.check,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                    ],
                  ),
                  onTap: item.enabled ? () {
                    Navigator.of(context).pop();
                    state.didChange(item.value);
                    onChanged?.call(item.value);
                  } : null,
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
          ],
        );
      },
    );
  }
}

/// 兼容性组件 - 保持原有的ThemedDropdownButton接口
/// 内部使用DialogSelector实现
class ThemedDropdownButton<T> extends StatelessWidget {
  final T? value;
  final String? hint;
  final List<DropdownMenuItem<T>> items;
  final ValueChanged<T?>? onChanged;
  final Widget? icon;
  final Color? iconEnabledColor;
  final Widget? underline;
  final bool isExpanded;
  final double? itemHeight;
  final FocusNode? focusNode;
  final bool autofocus;
  final Color? dropdownColor;

  const ThemedDropdownButton({
    super.key,
    this.value,
    this.hint,
    required this.items,
    this.onChanged,
    this.icon,
    this.iconEnabledColor,
    this.underline,
    this.isExpanded = false,
    this.itemHeight,
    this.focusNode,
    this.autofocus = false,
    this.dropdownColor,
  });

  @override
  Widget build(BuildContext context) {
    // 将DropdownMenuItem转换为DialogSelectItem
    final dialogItems = items.map((item) => DialogSelectItem<T>(
      value: item.value!,
      text: _extractTextFromWidget(item.child),
      leading: _extractLeadingFromWidget(item.child),
    )).toList();

    return DialogSelector<T>(
      value: value,
      hint: hint,
      items: dialogItems,
      onChanged: onChanged,
      icon: icon,
      isExpanded: isExpanded,
      title: '请选择',
    );
  }

  String _extractTextFromWidget(Widget widget) {
    if (widget is Text) {
      return widget.data ?? '';
    } else if (widget is Row) {
      // 处理包含图标和文本的Row
      for (final child in widget.children) {
        if (child is Text) {
          return child.data ?? '';
        } else if (child is Expanded && child.child is Text) {
          return (child.child as Text).data ?? '';
        }
      }
    }
    return '选项';
  }

  Widget? _extractLeadingFromWidget(Widget widget) {
    if (widget is Row) {
      for (final child in widget.children) {
        if (child is Icon) {
          return child;
        }
      }
    }
    return null;
  }
}

/// 兼容性组件 - 保持原有的ThemedDropdownButtonFormField接口
/// 内部使用DialogSelectorFormField实现
class ThemedDropdownButtonFormField<T> extends StatelessWidget {
  final T? value;
  final List<DropdownMenuItem<T>> items;
  final ValueChanged<T?>? onChanged;
  final InputDecoration? decoration;
  final FormFieldValidator<T>? validator;
  final FormFieldSetter<T>? onSaved;
  final Widget? icon;
  final Color? iconEnabledColor;
  final bool isExpanded;
  final double? itemHeight;
  final FocusNode? focusNode;
  final bool autofocus;
  final Color? dropdownColor;

  const ThemedDropdownButtonFormField({
    super.key,
    this.value,
    required this.items,
    this.onChanged,
    this.decoration,
    this.validator,
    this.onSaved,
    this.icon,
    this.iconEnabledColor,
    this.isExpanded = false,
    this.itemHeight,
    this.focusNode,
    this.autofocus = false,
    this.dropdownColor,
  });

  @override
  Widget build(BuildContext context) {
    // 将DropdownMenuItem转换为DialogSelectItem
    final dialogItems = items.map((item) => DialogSelectItem<T>(
      value: item.value!,
      text: _extractTextFromWidget(item.child),
      leading: _extractLeadingFromWidget(item.child),
    )).toList();

    return DialogSelectorFormField<T>(
      initialValue: value,
      items: dialogItems,
      decoration: decoration,
      validator: validator,
      onSaved: onSaved,
      icon: icon,
      isExpanded: isExpanded,
      title: '请选择',
    );
  }

  String _extractTextFromWidget(Widget widget) {
    if (widget is Text) {
      return widget.data ?? '';
    } else if (widget is Row) {
      // 处理包含图标和文本的Row
      for (final child in widget.children) {
        if (child is Text) {
          return child.data ?? '';
        } else if (child is Expanded && child.child is Text) {
          return (child.child as Text).data ?? '';
        }
      }
    }
    return '选项';
  }

  Widget? _extractLeadingFromWidget(Widget widget) {
    if (widget is Row) {
      for (final child in widget.children) {
        if (child is Icon) {
          return child;
        }
      }
    }
    return null;
  }
}
